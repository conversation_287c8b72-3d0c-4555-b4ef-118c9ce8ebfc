Name: editor-api
Host: 0.0.0.0
Port: 8890

App:
  BaseURL: http://localhost:8890

Log:
  ServiceName: editor-api
  Mode: console
  Level: info
  Encoding: plain

Auth:
  AccessSecret: jwt_1506900502
  AccessExpire: 86400  # 24小时

Database:
  Driver: postgres
  Source: host=paper-editor-postgres port=5432 user=postgres password=password dbname=paper_editor sslmode=disable

Cache:
  - Host: localhost:6379
    Pass: 
    Type: node

FileStorage:
  Type: minio  # 可选: local, oss, cos, minio
  LocalPath: ./uploads  # 本地存储路径，作为备用
  Endpoint: "*************:9000"  # MinIO服务器地址
  AccessKey: "minioadmin"  # MinIO访问密钥
  SecretKey: "minioadmin"  # MinIO密钥
  Bucket: "paper-editor"  # MinIO存储桶名称

Elasticsearch:
  Addresses:
    - http://localhost:9200
  Username: elastic
  Password: changeme
  Index: knowledge_vectors  # 添加Index字段
  Indices:
    Knowledge: knowledge_vectors
  Settings:
    RequestTimeout: 30s
    MaxRetries: 3
    RetryBackoff: 1s

RAG:
  ChunkSize: 1000
  ChunkOverlap: 200
  EmbeddingModel: text-embedding-3-small
  SimilarityTopK: 5
  SimilarityThreshold: 0.7

N8N:
  BaseURL: http://localhost:5678
  Username: admin
  Password: password
  WebhookURL: http://localhost:5678/webhook
