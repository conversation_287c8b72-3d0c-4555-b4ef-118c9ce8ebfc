#!/bin/bash

# 论文生成服务停止脚本
# 停止 dify-main、paper-editor-api、paper-node-service、paper-py-service

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🛑 开始停止论文生成服务..."

# 停止 Dify 服务
log_info "停止 Dify 服务..."
cd dify-main/docker
if docker-compose ps -q | grep -q .; then
    docker-compose down
    log_success "Dify 服务已停止"
else
    log_warning "Dify 服务未运行"
fi
cd ../..

# 停止 Paper Editor API 服务
log_info "停止 Paper Editor API 服务..."
if pgrep -f "go run editor.go" > /dev/null; then
    pkill -f "go run editor.go"
    log_success "Paper Editor API 服务已停止"
else
    log_warning "Paper Editor API 服务未运行"
fi

# 停止 Paper Node Service
log_info "停止 Paper Node Service..."
if pgrep -f "npm start" > /dev/null || pgrep -f "node app.js" > /dev/null; then
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "node app.js" 2>/dev/null || true
    log_success "Paper Node Service 已停止"
else
    log_warning "Paper Node Service 未运行"
fi

# 停止 Paper Python Service
log_info "停止 Paper Python Service..."
if pgrep -f "flask run" > /dev/null || pgrep -f "python.*app.py" > /dev/null; then
    pkill -f "flask run" 2>/dev/null || true
    pkill -f "python.*app.py" 2>/dev/null || true
    log_success "Paper Python Service 已停止"
else
    log_warning "Paper Python Service 未运行"
fi

# 检查端口占用情况
log_info "检查端口占用情况..."
PORTS=(3000 5001 8890 9529 9528)
for port in "${PORTS[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 仍被占用"
        # 尝试强制停止占用端口的进程
        PID=$(lsof -ti:$port)
        if [ ! -z "$PID" ]; then
            kill -9 $PID 2>/dev/null || true
            log_info "已强制停止占用端口 $port 的进程 (PID: $PID)"
        fi
    fi
done

# 清理临时文件
log_info "清理临时文件..."
rm -f logs/*.log 2>/dev/null || true

echo ""
log_success "所有服务已停止！"
echo ""
echo "🔄 重新启动服务："
echo "  ./start-services.sh"
echo ""
echo "📊 查看 Docker 状态："
echo "  docker ps -a"
echo ""
echo "🧹 清理 Docker 资源："
echo "  docker system prune -f"
