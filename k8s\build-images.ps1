# PowerShell 脚本 - 构建所有 Docker 镜像
param(
    [switch]$Force = $false
)

Write-Host "开始构建 Docker 镜像..." -ForegroundColor Green

# 设置项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot

# 构建 paper-editor-api
Write-Host "构建 paper-editor-api..." -ForegroundColor Yellow
Set-Location "$ProjectRoot\paper-editor-api"
docker build -t paper-editor-api:latest .
if ($LASTEXITCODE -ne 0) {
    Write-Error "paper-editor-api 构建失败"
    exit 1
}

# 构建 lunwen-generate-ui
Write-Host "构建 lunwen-generate-ui..." -ForegroundColor Yellow
Set-Location "$ProjectRoot\lunwen-generate-ui"
docker build -t lunwen-generate-ui:latest .
if ($LASTEXITCODE -ne 0) {
    Write-Error "lunwen-generate-ui 构建失败"
    exit 1
}

# 构建 paper-node-service
Write-Host "构建 paper-node-service..." -ForegroundColor Yellow
Set-Location "$ProjectRoot\paper-node-service"
docker build -t paper-node-service:latest .
if ($LASTEXITCODE -ne 0) {
    Write-Error "paper-node-service 构建失败"
    exit 1
}

# 构建 paper-py-service
Write-Host "构建 paper-py-service..." -ForegroundColor Yellow
Set-Location "$ProjectRoot\paper-py-service"
docker build -t paper-py-service:latest .
if ($LASTEXITCODE -ne 0) {
    Write-Error "paper-py-service 构建失败"
    exit 1
}

# # 构建 paper-api-service (如果存在)
# if (Test-Path "$ProjectRoot\paper-api-service\Dockerfile") {
#     Write-Host "构建 paper-api-service..." -ForegroundColor Yellow
#     Set-Location "$ProjectRoot\paper-api-service"
#     docker build -t paper-api-service:latest .
#     if ($LASTEXITCODE -ne 0) {
#         Write-Error "paper-api-service 构建失败"
#         exit 1
#     }
# }

Write-Host "所有镜像构建完成!" -ForegroundColor Green

# 显示构建的镜像
Write-Host "`n构建的镜像列表:" -ForegroundColor Cyan
docker images | Select-String "paper-|lunwen-"
