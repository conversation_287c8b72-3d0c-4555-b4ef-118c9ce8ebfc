#!/bin/bash

# 论文生成服务启动脚本
# 启动 dify-main、paper-editor-api、paper-node-service、paper-py-service

echo "🚀 开始启动论文生成服务..."

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建数据目录..."
mkdir -p volumes/dify/{db,redis,storage}
mkdir -p volumes/paper-editor/postgres
mkdir -p volumes/weaviate
mkdir -p paper-editor-api/uploads

# 设置权限
chmod -R 755 volumes/

echo "🔧 构建和启动服务..."

# 构建并启动所有服务
docker-compose up -d --build

echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

echo ""
echo "✅ 服务启动完成！"
echo ""
echo "🌐 服务访问地址："
echo "  - Dify Web UI:        http://localhost:3000"
echo "  - Dify API:           http://localhost:5001"
echo "  - Paper Editor API:   http://localhost:8890"
echo "  - Paper Node Service: http://localhost:9529"
echo "  - Paper Py Service:   http://localhost:9528"
echo "  - Weaviate:           http://localhost:8080"
echo "  - PostgreSQL:         localhost:5432"
echo ""
echo "📝 查看日志："
echo "  docker-compose logs -f [service_name]"
echo ""
echo "🛑 停止服务："
echo "  docker-compose down"
echo ""
echo "🔄 重启服务："
echo "  docker-compose restart [service_name]"
