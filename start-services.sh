#!/bin/bash

# 论文生成服务一键启动脚本 (macOS/Linux)
# 支持开发模式和生产模式
# 启动 dify-main、paper-editor-api、paper-node-service、paper-py-service

set -e

# 默认模式为开发模式
MODE="dev"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --mode|-m)
            MODE="$2"
            shift 2
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -m, --mode MODE    运行模式 (dev|prod)，默认: dev"
            echo "  -h, --help         显示帮助信息"
            echo ""
            echo "模式说明:"
            echo "  dev  - 开发模式: Paper Editor API 使用 docker-compose + go run"
            echo "  prod - 生产模式: 所有服务使用 Docker 容器运行"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 验证模式参数
if [[ "$MODE" != "dev" && "$MODE" != "prod" ]]; then
    echo "错误: 模式必须是 'dev' 或 'prod'"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "等待 $service_name 启动..."

    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "$service_name 启动成功"
            return 0
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "$service_name 启动超时"
    return 1
}

# 清理函数
cleanup() {
    log_info "正在清理进程..."

    # 停止 Docker Compose 服务
    if [ "$MODE" = "dev" ]; then
        cd paper-editor-api && docker-compose down 2>/dev/null || true && cd ..
    fi
    cd dify-main/docker && docker-compose down 2>/dev/null || true && cd ../..

    # 停止后台进程
    pkill -f "go run editor.go" 2>/dev/null || true
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "node app.js" 2>/dev/null || true
    pkill -f "flask run" 2>/dev/null || true

    log_info "清理完成"
}

# 设置信号处理
trap cleanup EXIT INT TERM

echo -e "${CYAN}🚀 开始启动论文生成服务 (${MODE} 模式)...${NC}"

# 检查必要的命令
log_info "检查系统依赖..."
check_command "docker"
check_command "docker-compose"

if [ "$MODE" = "dev" ]; then
    check_command "go"
    check_command "node"
    check_command "python3"
fi

# 检查端口占用
log_info "检查端口占用情况..."
if [ "$MODE" = "dev" ]; then
    PORTS=(3000 5001 8890 9529 9528 5432 9200 6379 9000)
else
    PORTS=(3000 5001 8890 9529 9528 8080 5432)
fi

for port in "${PORTS[@]}"; do
    if ! check_port $port; then
        log_error "请先停止占用端口 $port 的服务"
        exit 1
    fi
done

# 创建必要的目录
log_info "创建数据目录..."
mkdir -p logs
if [ "$MODE" = "dev" ]; then
    mkdir -p paper-editor-api/uploads
fi

# 启动服务
if [ "$MODE" = "dev" ]; then
    # 开发模式
    log_info "=== 开发模式启动 ==="

    # 1. 启动 Dify 服务
    log_info "启动 Dify 服务..."
    cd dify-main/docker
    docker-compose up -d
    cd ../..

    # 2. 启动 Paper Editor API 的依赖服务
    log_info "启动 Paper Editor API 依赖服务..."
    cd paper-editor-api
    docker-compose up -d
    cd ..

    # 等待数据库服务启动
    log_info "等待数据库服务启动..."
    sleep 15

    # 3. 启动 Paper Editor API Go 服务
    log_info "启动 Paper Editor API Go 服务..."
    cd paper-editor-api
    go mod tidy
    nohup go run editor.go -e dev > ../logs/paper-editor-api.log 2>&1 &
    cd ..

    # 4. 启动 Paper Node Service
    log_info "启动 Paper Node Service..."
    cd paper-node-service
    npm install
    nohup npm start > ../logs/paper-node-service.log 2>&1 &
    cd ..

    # 5. 启动 Paper Python Service
    log_info "启动 Paper Python Service..."
    cd paper-py-service
    pip3 install -r requirements.txt
    export FLASK_APP=app.py
    export FLASK_ENV=development
    nohup python3 -m flask run --host=0.0.0.0 --port=9528 > ../logs/paper-py-service.log 2>&1 &
    cd ..

else
    # 生产模式
    log_info "=== 生产模式启动 ==="

    # 1. 启动 Dify 服务
    log_info "启动 Dify 服务..."
    cd dify-main/docker
    docker-compose up -d
    cd ../..

    # 2. 启动 Paper Editor API (Docker)
    log_info "启动 Paper Editor API..."
    cd paper-editor-api
    docker-compose up -d
    cd ..

    # 3. 启动 Paper Node Service (Docker)
    log_info "启动 Paper Node Service..."
    cd paper-node-service
    docker build -t paper-node-service .
    docker run -d --name paper-node-service -p 9529:9529 paper-node-service
    cd ..

    # 4. 启动 Paper Python Service (Docker)
    log_info "启动 Paper Python Service..."
    cd paper-py-service
    docker build -t paper-py-service .
    docker run -d --name paper-py-service -p 9528:9528 paper-py-service
    cd ..
fi

# 等待所有服务启动
log_info "等待所有服务启动完成..."
sleep 30

# 检查服务状态
log_info "检查服务状态..."

# 检查 Dify 服务
if wait_for_service "http://localhost:3000" "Dify Web UI"; then
    log_success "Dify Web UI 运行正常"
else
    log_warning "Dify Web UI 可能需要更多时间启动"
fi

if wait_for_service "http://localhost:5001/health" "Dify API"; then
    log_success "Dify API 运行正常"
else
    log_warning "Dify API 可能需要更多时间启动"
fi

# 检查其他服务
if wait_for_service "http://localhost:8890/health" "Paper Editor API"; then
    log_success "Paper Editor API 运行正常"
else
    log_warning "Paper Editor API 可能需要更多时间启动"
fi

if wait_for_service "http://localhost:9529/health" "Paper Node Service"; then
    log_success "Paper Node Service 运行正常"
else
    log_warning "Paper Node Service 可能需要更多时间启动"
fi

if wait_for_service "http://localhost:9528/health" "Paper Python Service"; then
    log_success "Paper Python Service 运行正常"
else
    log_warning "Paper Python Service 可能需要更多时间启动"
fi

echo ""
log_success "所有服务启动完成！"
echo ""
echo -e "${CYAN}🌐 服务访问地址：${NC}"
echo "  - Dify Web UI:        http://localhost:3000"
echo "  - Dify API:           http://localhost:5001"
echo "  - Paper Editor API:   http://localhost:8890"
echo "  - Paper Node Service: http://localhost:9529"
echo "  - Paper Python Service: http://localhost:9528"

if [ "$MODE" = "dev" ]; then
    echo "  - PostgreSQL:         localhost:5432"
    echo "  - Elasticsearch:      http://localhost:9200"
    echo "  - Kibana:             http://localhost:5601"
    echo "  - Redis:              localhost:6379"
    echo "  - MinIO:              http://localhost:9000"
    echo "  - MinIO Console:      http://localhost:9001"
    echo "  - n8n:                http://localhost:5678"
else
    echo "  - Weaviate:           http://localhost:8080"
    echo "  - PostgreSQL:         localhost:5432"
fi

echo ""
echo -e "${CYAN}📝 查看日志：${NC}"
echo "  - Dify: cd dify-main/docker && docker-compose logs -f"

if [ "$MODE" = "dev" ]; then
    echo "  - Paper Editor API: tail -f logs/paper-editor-api.log"
    echo "  - Paper Node Service: tail -f logs/paper-node-service.log"
    echo "  - Paper Python Service: tail -f logs/paper-py-service.log"
    echo "  - Paper Editor 依赖: cd paper-editor-api && docker-compose logs -f"
else
    echo "  - Paper Editor API: cd paper-editor-api && docker-compose logs -f"
    echo "  - Paper Node Service: docker logs -f paper-node-service"
    echo "  - Paper Python Service: docker logs -f paper-py-service"
fi

echo ""
echo -e "${CYAN}🛑 停止所有服务：${NC}"
echo "  按 Ctrl+C 或运行清理脚本"
echo ""
echo -e "${YELLOW}按 Ctrl+C 停止所有服务...${NC}"

# 保持脚本运行，直到用户按 Ctrl+C
while true; do
    sleep 1
done
