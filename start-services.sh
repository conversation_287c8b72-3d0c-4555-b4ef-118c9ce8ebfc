#!/bin/bash

# 论文生成服务一键启动脚本
# 启动 dify-main、paper-editor-api、paper-node-service、paper-py-service

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "等待 $service_name 启动..."

    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "$service_name 启动成功"
            return 0
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "$service_name 启动超时"
    return 1
}

# 清理函数
cleanup() {
    log_info "正在清理进程..."

    # 停止后台进程
    if [ ! -z "$DIFY_PID" ]; then
        kill $DIFY_PID 2>/dev/null || true
    fi

    if [ ! -z "$EDITOR_PID" ]; then
        kill $EDITOR_PID 2>/dev/null || true
    fi

    if [ ! -z "$NODE_PID" ]; then
        kill $NODE_PID 2>/dev/null || true
    fi

    if [ ! -z "$PYTHON_PID" ]; then
        kill $PYTHON_PID 2>/dev/null || true
    fi

    log_info "清理完成"
}

# 设置信号处理
trap cleanup EXIT INT TERM

echo "🚀 开始启动论文生成服务..."

# 检查必要的命令
log_info "检查系统依赖..."
check_command "docker"
check_command "docker-compose"
check_command "go"
check_command "node"
check_command "python3"

# 检查端口占用
log_info "检查端口占用情况..."
PORTS=(3000 5001 8890 9529 9528 8080 5432)
for port in "${PORTS[@]}"; do
    if ! check_port $port; then
        log_error "请先停止占用端口 $port 的服务"
        exit 1
    fi
done

# 创建必要的目录
log_info "创建数据目录..."
mkdir -p volumes/dify/{db,redis,storage}
mkdir -p volumes/paper-editor/postgres
mkdir -p volumes/weaviate
mkdir -p paper-editor-api/uploads
mkdir -p logs

# 设置权限
chmod -R 755 volumes/ 2>/dev/null || true
chmod -R 755 logs/ 2>/dev/null || true

# 启动 Dify 服务
log_info "启动 Dify 服务..."
cd dify-main/docker
docker-compose up -d
DIFY_PID=$!
cd ../..

# 等待 Dify 服务启动
sleep 10

# 启动 Paper Editor API 服务
log_info "启动 Paper Editor API 服务..."
cd paper-editor-api
go mod tidy
nohup go run editor.go -f etc/editor-api-dev.yaml > ../logs/paper-editor-api.log 2>&1 &
EDITOR_PID=$!
cd ..

# 启动 Paper Node Service
log_info "启动 Paper Node Service..."
cd paper-node-service
npm install
nohup npm start > ../logs/paper-node-service.log 2>&1 &
NODE_PID=$!
cd ..

# 启动 Paper Python Service
log_info "启动 Paper Python Service..."
cd paper-py-service
pip3 install -r requirements.txt
nohup python3 -m flask run --host=0.0.0.0 --port=9528 > ../logs/paper-py-service.log 2>&1 &
PYTHON_PID=$!
cd ..

# 等待所有服务启动
log_info "等待所有服务启动完成..."
sleep 30

# 检查服务状态
log_info "检查服务状态..."

# 检查 Dify 服务
if wait_for_service "http://localhost:3000" "Dify Web UI"; then
    log_success "Dify Web UI 运行正常"
else
    log_error "Dify Web UI 启动失败"
fi

if wait_for_service "http://localhost:5001/health" "Dify API"; then
    log_success "Dify API 运行正常"
else
    log_warning "Dify API 可能需要更多时间启动"
fi

# 检查其他服务
if wait_for_service "http://localhost:8890/health" "Paper Editor API"; then
    log_success "Paper Editor API 运行正常"
else
    log_warning "Paper Editor API 可能需要更多时间启动"
fi

if wait_for_service "http://localhost:9529/health" "Paper Node Service"; then
    log_success "Paper Node Service 运行正常"
else
    log_warning "Paper Node Service 可能需要更多时间启动"
fi

if wait_for_service "http://localhost:9528/health" "Paper Python Service"; then
    log_success "Paper Python Service 运行正常"
else
    log_warning "Paper Python Service 可能需要更多时间启动"
fi

echo ""
log_success "所有服务启动完成！"
echo ""
echo "🌐 服务访问地址："
echo "  - Dify Web UI:        http://localhost:3000"
echo "  - Dify API:           http://localhost:5001"
echo "  - Paper Editor API:   http://localhost:8890"
echo "  - Paper Node Service: http://localhost:9529"
echo "  - Paper Python Service: http://localhost:9528"
echo "  - Weaviate:           http://localhost:8080"
echo "  - PostgreSQL:         localhost:5432"
echo ""
echo "📝 查看日志："
echo "  - Dify: cd dify-main/docker && docker-compose logs -f"
echo "  - Paper Editor API: tail -f logs/paper-editor-api.log"
echo "  - Paper Node Service: tail -f logs/paper-node-service.log"
echo "  - Paper Python Service: tail -f logs/paper-py-service.log"
echo ""
echo "🛑 停止所有服务："
echo "  ./stop-services.sh"
echo ""
echo "🔄 重启服务："
echo "  ./restart-services.sh"
echo ""
echo "按 Ctrl+C 停止所有服务..."

# 保持脚本运行，直到用户按 Ctrl+C
while true; do
    sleep 1
done
