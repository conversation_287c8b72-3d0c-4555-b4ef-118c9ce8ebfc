@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 论文生成服务停止脚本 (Windows批处理版)
:: 停止 dify-main、paper-editor-api、paper-node-service、paper-py-service

echo 🛑 开始停止论文生成服务...
echo.

:: 停止 Dify 服务
echo [INFO] 停止 Dify 服务...
cd dify-main\docker
docker-compose down
if errorlevel 1 (
    echo [WARNING] 停止 Dify 服务时出现问题
) else (
    echo [SUCCESS] Dify 服务已停止
)
cd ..\..

:: 停止 Go 进程 (Paper Editor API)
echo [INFO] 停止 Paper Editor API 服务...
taskkill /f /im go.exe >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Paper Editor API 服务未运行或停止失败
) else (
    echo [SUCCESS] Paper Editor API 服务已停止
)

:: 停止 Node.js 进程 (Paper Node Service)
echo [INFO] 停止 Paper Node Service...
taskkill /f /im node.exe >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Paper Node Service 未运行或停止失败
) else (
    echo [SUCCESS] Paper Node Service 已停止
)

:: 停止 Python 进程 (Paper Python Service)
echo [INFO] 停止 Paper Python Service...
taskkill /f /im python.exe >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Paper Python Service 未运行或停止失败
) else (
    echo [SUCCESS] Paper Python Service 已停止
)

:: 停止可能的 npm 进程
taskkill /f /im npm.exe >nul 2>&1

:: 清理临时文件
echo [INFO] 清理临时文件...
if exist "logs\*.log" del /q "logs\*.log" >nul 2>&1

echo.
echo [SUCCESS] 所有服务已停止！
echo.
echo 🔄 重新启动服务：
echo   start-services.bat
echo.
echo 📊 查看 Docker 状态：
echo   docker ps -a
echo.
echo 🧹 清理 Docker 资源：
echo   docker system prune -f
echo.
echo 按任意键退出...
pause >nul
