# Quick Start Script
Write-Host '=== Paper Services K8s Quick Start ===' -ForegroundColor Green

try {
    docker version | Out-Null
    Write-Host 'Docker is running' -ForegroundColor Green
} catch {
    Write-Error 'Docker is not running, please start Docker Desktop'
    exit 1
}

try {
    kubectl cluster-info | Out-Null
    Write-Host 'Kubernetes cluster is available' -ForegroundColor Green
} catch {
    Write-Error 'Kubernetes is not available, please enable Kubernetes in Docker Desktop'
    exit 1
}

Write-Host 'Starting deployment...' -ForegroundColor Yellow
& " $PSScriptRoot\deploy.ps1\ -Build

Write-Host 'Deployment complete!' -ForegroundColor Green
Write-Host 'Paper Generation System: http://localhost:30000' -ForegroundColor White
Write-Host 'Dify AI Platform: http://localhost:30001' -ForegroundColor White
