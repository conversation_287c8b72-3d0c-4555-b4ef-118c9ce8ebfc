# 论文生成服务 Docker 部署指南

本指南介绍如何使用 Docker 和 Docker Compose 启动论文生成系统的所有服务。

## 🏗️ 服务架构

系统包含以下服务：

### 核心服务
- **dify-main**: AI 对话和工作流平台
  - `dify-api`: API 服务 (端口: 5001)
  - `dify-worker`: 后台任务处理
  - `dify-web`: Web 界面 (端口: 3000)

- **paper-editor-api**: 论文编辑 API 服务 (端口: 8890)
- **paper-node-service**: Node.js 服务 (端口: 9529)  
- **paper-py-service**: Python 服务 (端口: 9528)

### 数据库服务
- **PostgreSQL**: 
  - Dify 数据库 (内部端口: 5432)
  - Paper Editor 数据库 (端口: 5432)
- **Redis**: 缓存和消息队列 (内部端口: 6379)
- **Weaviate**: 向量数据库 (端口: 8080)

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

```bash
# 给脚本执行权限
chmod +x start-services.sh

# 启动所有服务
./start-services.sh
```

### 方法二：手动启动

```bash
# 创建数据目录
mkdir -p volumes/dify/{db,redis,storage}
mkdir -p volumes/paper-editor/postgres
mkdir -p volumes/weaviate
mkdir -p paper-editor-api/uploads

# 构建并启动服务
docker-compose up -d --build

# 查看服务状态
docker-compose ps
```

## 🔧 单独启动某个服务

### 启动 paper-node-service

```bash
# 进入服务目录
cd paper-node-service

# 构建镜像
docker build -t paper-node-service .

# 运行容器
docker run -d -p 9529:9529 --name paper-node-service paper-node-service
```

### 启动 paper-editor-api

```bash
cd paper-editor-api
docker build -t paper-editor-api .
docker run -d -p 8890:8890 --name paper-editor-api paper-editor-api
```

### 启动 paper-py-service

```bash
cd paper-py-service
docker build -t paper-py-service .
docker run -d -p 9528:9528 --name paper-py-service paper-py-service
```

## 📊 服务管理

### 查看服务状态
```bash
docker-compose ps
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f dify-api
docker-compose logs -f paper-editor-api
docker-compose logs -f paper-node-service
docker-compose logs -f paper-py-service
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart dify-api
```

### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问各个服务：

- **Dify Web UI**: http://localhost:3000
- **Dify API**: http://localhost:5001
- **Paper Editor API**: http://localhost:8890
- **Paper Node Service**: http://localhost:9529
- **Paper Python Service**: http://localhost:9528
- **Weaviate**: http://localhost:8080
- **PostgreSQL**: localhost:5432

## 🔍 故障排除

### 端口冲突
如果遇到端口冲突，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "新端口:容器端口"
```

### 数据持久化
所有数据都存储在 `volumes/` 目录下：
- `volumes/dify/`: Dify 相关数据
- `volumes/paper-editor/`: Paper Editor 数据
- `volumes/weaviate/`: 向量数据库数据

### 清理和重置
```bash
# 停止所有服务并删除容器
docker-compose down

# 删除所有数据（谨慎操作）
sudo rm -rf volumes/

# 重新创建目录
mkdir -p volumes/dify/{db,redis,storage}
mkdir -p volumes/paper-editor/postgres
mkdir -p volumes/weaviate

# 重新启动
docker-compose up -d --build
```

## 📝 配置说明

### 环境变量
主要配置在 `docker-compose.yml` 中，包括：
- 数据库连接信息
- Redis 配置
- API 密钥
- 存储配置

### 自定义配置
如需自定义配置，可以：
1. 修改 `docker-compose.yml` 中的环境变量
2. 创建 `.env` 文件设置环境变量
3. 挂载自定义配置文件

## 🔐 安全注意事项

1. 修改默认密码（数据库、Redis 等）
2. 在生产环境中使用强密码
3. 配置防火墙规则
4. 定期备份数据

## 📞 技术支持

如遇问题，请检查：
1. Docker 和 Docker Compose 版本
2. 系统资源（内存、磁盘空间）
3. 网络连接
4. 服务日志信息
