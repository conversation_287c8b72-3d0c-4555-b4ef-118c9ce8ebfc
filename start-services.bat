@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 论文生成服务一键启动脚本 (Windows批处理版)
:: 启动 dify-main、paper-editor-api、paper-node-service、paper-py-service

echo 🚀 开始启动论文生成服务...
echo.

:: 检查必要的命令
echo [INFO] 检查系统依赖...
where docker >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker
    pause
    exit /b 1
)

where docker-compose >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

where go >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Go 未安装，请先安装 Go
    pause
    exit /b 1
)

where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

where python >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python 未安装，请先安装 Python
    pause
    exit /b 1
)

:: 创建必要的目录
echo [INFO] 创建数据目录...
if not exist "volumes\dify\db" mkdir "volumes\dify\db"
if not exist "volumes\dify\redis" mkdir "volumes\dify\redis"
if not exist "volumes\dify\storage" mkdir "volumes\dify\storage"
if not exist "volumes\paper-editor\postgres" mkdir "volumes\paper-editor\postgres"
if not exist "volumes\weaviate" mkdir "volumes\weaviate"
if not exist "paper-editor-api\uploads" mkdir "paper-editor-api\uploads"
if not exist "logs" mkdir "logs"

:: 启动 Dify 服务
echo [INFO] 启动 Dify 服务...
cd dify-main\docker
start /b docker-compose up -d
cd ..\..

:: 等待 Dify 服务启动
echo [INFO] 等待 Dify 服务启动...
timeout /t 15 /nobreak >nul

:: 启动 Paper Editor API 服务
echo [INFO] 启动 Paper Editor API 服务...
cd paper-editor-api
start /b cmd /c "go mod tidy && go run editor.go -f etc\editor-api-dev.yaml > ..\logs\paper-editor-api.log 2>&1"
cd ..

:: 启动 Paper Node Service
echo [INFO] 启动 Paper Node Service...
cd paper-node-service
start /b cmd /c "npm install && npm start > ..\logs\paper-node-service.log 2>&1"
cd ..

:: 启动 Paper Python Service
echo [INFO] 启动 Paper Python Service...
cd paper-py-service
start /b cmd /c "pip install -r requirements.txt && set FLASK_APP=app.py && set FLASK_ENV=development && python -m flask run --host=0.0.0.0 --port=9528 > ..\logs\paper-py-service.log 2>&1"
cd ..

:: 等待所有服务启动
echo [INFO] 等待所有服务启动完成...
timeout /t 30 /nobreak >nul

echo.
echo [SUCCESS] 所有服务启动完成！
echo.
echo 🌐 服务访问地址：
echo   - Dify Web UI:        http://localhost:3000
echo   - Dify API:           http://localhost:5001
echo   - Paper Editor API:   http://localhost:8890
echo   - Paper Node Service: http://localhost:9529
echo   - Paper Python Service: http://localhost:9528
echo   - Weaviate:           http://localhost:8080
echo   - PostgreSQL:         localhost:5432
echo.
echo 📝 查看日志：
echo   - Dify: cd dify-main\docker ^&^& docker-compose logs -f
echo   - Paper Editor API: type logs\paper-editor-api.log
echo   - Paper Node Service: type logs\paper-node-service.log
echo   - Paper Python Service: type logs\paper-py-service.log
echo.
echo 🛑 停止所有服务：
echo   stop-services.bat
echo.
echo 按任意键退出...
pause >nul
