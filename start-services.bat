@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 论文生成服务一键启动脚本 (Windows)
:: 支持开发模式和生产模式
:: 启动 dify-main、paper-editor-api、paper-node-service、paper-py-service

:: 默认模式为开发模式
set "MODE=dev"

:: 解析命令行参数
:parse_args
if "%~1"=="" goto start_script
if "%~1"=="--mode" (
    set "MODE=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="-m" (
    set "MODE=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="--help" goto show_help
if "%~1"=="-h" goto show_help
echo 未知参数: %~1
echo 使用 --help 查看帮助
exit /b 1

:show_help
echo 用法: %~nx0 [选项]
echo 选项:
echo   -m, --mode MODE    运行模式 (dev^|prod)，默认: dev
echo   -h, --help         显示帮助信息
echo.
echo 模式说明:
echo   dev  - 开发模式: Paper Editor API 使用 docker-compose + go run
echo   prod - 生产模式: 所有服务使用 Docker 容器运行
exit /b 0

:start_script
:: 验证模式参数
if not "%MODE%"=="dev" if not "%MODE%"=="prod" (
    echo [ERROR] 模式必须是 'dev' 或 'prod'
    exit /b 1
)

echo 🚀 开始启动论文生成服务 (%MODE% 模式)...
echo.

:: 检查必要的命令
echo [INFO] 检查系统依赖...
where docker >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker
    pause
    exit /b 1
)

where docker-compose >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

if "%MODE%"=="dev" (
    where go >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Go 未安装，请先安装 Go
        pause
        exit /b 1
    )

    where node >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Node.js 未安装，请先安装 Node.js
        pause
        exit /b 1
    )

    where python >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Python 未安装，请先安装 Python
        pause
        exit /b 1
    )
)

:: 创建必要的目录
echo [INFO] 创建数据目录...
if not exist "logs" mkdir "logs"
if "%MODE%"=="dev" (
    if not exist "paper-editor-api\uploads" mkdir "paper-editor-api\uploads"
)

:: 启动服务
if "%MODE%"=="dev" (
    echo [INFO] === 开发模式启动 ===

    :: 1. 启动 Dify 服务
    echo [INFO] 启动 Dify 服务...
    cd dify-main\docker
    start /b docker-compose up -d
    cd ..\..

    :: 2. 启动 Paper Editor API 的依赖服务
    echo [INFO] 启动 Paper Editor API 依赖服务...
    cd paper-editor-api
    start /b docker-compose up -d
    cd ..

    :: 等待数据库服务启动
    echo [INFO] 等待数据库服务启动...
    timeout /t 15 /nobreak >nul

    :: 3. 启动 Paper Editor API Go 服务
    echo [INFO] 启动 Paper Editor API Go 服务...
    cd paper-editor-api
    start /b cmd /c "go mod tidy && go run editor.go -e dev > ..\logs\paper-editor-api.log 2>&1"
    cd ..

    :: 4. 启动 Paper Node Service
    echo [INFO] 启动 Paper Node Service...
    cd paper-node-service
    start /b cmd /c "npm install && npm start > ..\logs\paper-node-service.log 2>&1"
    cd ..

    :: 5. 启动 Paper Python Service
    echo [INFO] 启动 Paper Python Service...
    cd paper-py-service
    start /b cmd /c "pip install -r requirements.txt && set FLASK_APP=app.py && set FLASK_ENV=development && python -m flask run --host=0.0.0.0 --port=9528 > ..\logs\paper-py-service.log 2>&1"
    cd ..

) else (
    echo [INFO] === 生产模式启动 ===

    :: 1. 启动 Dify 服务
    echo [INFO] 启动 Dify 服务...
    cd dify-main\docker
    start /b docker-compose up -d
    cd ..\..

    :: 2. 启动 Paper Editor API (Docker)
    echo [INFO] 启动 Paper Editor API...
    cd paper-editor-api
    start /b docker-compose up -d
    cd ..

    :: 3. 启动 Paper Node Service (Docker)
    echo [INFO] 启动 Paper Node Service...
    cd paper-node-service
    docker build -t paper-node-service .
    docker run -d --name paper-node-service -p 9529:9529 paper-node-service
    cd ..

    :: 4. 启动 Paper Python Service (Docker)
    echo [INFO] 启动 Paper Python Service...
    cd paper-py-service
    docker build -t paper-py-service .
    docker run -d --name paper-py-service -p 9528:9528 paper-py-service
    cd ..
)

:: 等待所有服务启动
echo [INFO] 等待所有服务启动完成...
timeout /t 30 /nobreak >nul

echo.
echo [SUCCESS] 所有服务启动完成！
echo.
echo 🌐 服务访问地址：
echo   - Dify Web UI:        http://localhost:3000
echo   - Dify API:           http://localhost:5001
echo   - Paper Editor API:   http://localhost:8890
echo   - Paper Node Service: http://localhost:9529
echo   - Paper Python Service: http://localhost:9528

if "%MODE%"=="dev" (
    echo   - PostgreSQL:         localhost:5432
    echo   - Elasticsearch:      http://localhost:9200
    echo   - Kibana:             http://localhost:5601
    echo   - Redis:              localhost:6379
    echo   - MinIO:              http://localhost:9000
    echo   - MinIO Console:      http://localhost:9001
    echo   - n8n:                http://localhost:5678
) else (
    echo   - Weaviate:           http://localhost:8080
    echo   - PostgreSQL:         localhost:5432
)

echo.
echo 📝 查看日志：
echo   - Dify: cd dify-main\docker ^&^& docker-compose logs -f

if "%MODE%"=="dev" (
    echo   - Paper Editor API: type logs\paper-editor-api.log
    echo   - Paper Node Service: type logs\paper-node-service.log
    echo   - Paper Python Service: type logs\paper-py-service.log
    echo   - Paper Editor 依赖: cd paper-editor-api ^&^& docker-compose logs -f
) else (
    echo   - Paper Editor API: cd paper-editor-api ^&^& docker-compose logs -f
    echo   - Paper Node Service: docker logs -f paper-node-service
    echo   - Paper Python Service: docker logs -f paper-py-service
)

echo.
echo 🛑 停止所有服务：
echo   按 Ctrl+C 或手动停止 Docker 容器
echo.
echo 按任意键退出...
pause >nul
