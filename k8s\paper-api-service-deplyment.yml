apiVersion: apps/v1
kind: Deployment
metadata:
  name: paper-api-service-deployment
  labels:
    app: paper-api-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paper-api-service
  template:
    metadata:
      labels:
        app: paper-api-service
    spec:
      containers:
      - name: paper-api-service
        image: paper-api-service:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 9527
        env:
        - name: ENV
          value: "prod"
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: paper-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: paper-api-service
  labels:
    app: paper-api-service
spec:
  selector:
    app: paper-api-service
  ports:
    - protocol: TCP
      port: 9527
      targetPort: 9527
  type: ClusterIP
  