# Paper Editor API Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paper-editor-api-deployment
  namespace: paper-services
  labels:
    app: paper-editor-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paper-editor-api
  template:
    metadata:
      labels:
        app: paper-editor-api
    spec:
      containers:
      - name: paper-editor-api
        image: paper-editor-api:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8890
        env:
        - name: ENV
          value: "prod"
        volumeMounts:
        - name: config-volume
          mountPath: /root/etc
        - name: uploads-volume
          mountPath: /root/uploads
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: config-volume
        configMap:
          name: paper-editor-api-config
      - name: uploads-volume
        persistentVolumeClaim:
          claimName: paper-editor-uploads-pvc
---
# Paper Editor API Service
apiVersion: v1
kind: Service
metadata:
  name: paper-editor-api
  namespace: paper-services
  labels:
    app: paper-editor-api
spec:
  selector:
    app: paper-editor-api
  ports:
    - protocol: TCP
      port: 8890
      targetPort: 8890
  type: ClusterIP
---
# Paper Editor API ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: paper-editor-api-config
  namespace: paper-services
data:
  editor-api-prod.yaml: |
    Name: editor-api
    Host: 0.0.0.0
    Port: 8890

    App:
      BaseURL: http://paper-editor-api:8890

    Log:
      ServiceName: editor-api
      Mode: console
      Level: info
      Encoding: plain

    Auth:
      AccessSecret: jwt_1506900502
      AccessExpire: 86400

    Database:
      Driver: postgres
      Source: host=paper-editor-postgres port=5432 user=postgres password=paper_editor_pass dbname=paper_editor sslmode=disable

    Cache:
      - Host: paper-editor-redis:6379
        Pass: paper_editor_redis
        Type: node

    FileStorage:
      Type: local
      LocalPath: ./uploads

    RAG:
      ChunkSize: 1000
      ChunkOverlap: 200
      EmbeddingModel: text-embedding-3-small
      SimilarityTopK: 5
      SimilarityThreshold: 0.7
