# Lunwen Generate UI Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lunwen-generate-ui-deployment
  namespace: paper-services
  labels:
    app: lunwen-generate-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lunwen-generate-ui
  template:
    metadata:
      labels:
        app: lunwen-generate-ui
    spec:
      containers:
      - name: lunwen-generate-ui
        image: lunwen-generate-ui:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: HOSTNAME
          value: "0.0.0.0"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
# Lunwen Generate UI Service
apiVersion: v1
kind: Service
metadata:
  name: lunwen-generate-ui
  namespace: paper-services
  labels:
    app: lunwen-generate-ui
spec:
  selector:
    app: lunwen-generate-ui
  ports:
    - protocol: TCP
      port: 3000
      targetPort: 3000
      nodePort: 30000  # 外部访问端口
  type: NodePort
