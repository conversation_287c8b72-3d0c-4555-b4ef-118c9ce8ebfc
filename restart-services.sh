#!/bin/bash

# 论文生成服务重启脚本
# 重启 dify-main、paper-editor-api、paper-node-service、paper-py-service

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${CYAN}🔄 开始重启论文生成服务...${NC}"

# 检查脚本是否存在
if [ ! -f "./stop-services.sh" ]; then
    log_error "stop-services.sh 脚本不存在"
    exit 1
fi

if [ ! -f "./start-services.sh" ]; then
    log_error "start-services.sh 脚本不存在"
    exit 1
fi

# 设置脚本执行权限
chmod +x ./stop-services.sh
chmod +x ./start-services.sh

# 停止所有服务
log_info "第一步：停止所有服务..."
./stop-services.sh

# 等待一段时间确保所有进程完全停止
log_info "等待服务完全停止..."
sleep 5

# 启动所有服务
log_info "第二步：启动所有服务..."
./start-services.sh
