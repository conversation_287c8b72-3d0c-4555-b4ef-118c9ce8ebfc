# Python 服务的 Deployment 配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paper-py-service-deployment
  labels:
    app: paper-py-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paper-py-service
  template:
    metadata:
      labels:
        app: paper-py-service
    spec:
      containers:
      - name: paper-py-service
        image: paper-py-service:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 9528
        env:
        - name: FLASK_ENV
          value: "production"
        - name: FLASK_RUN_PORT
          value: "9528"
        - name: FLASK_CONFIG
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
# Python 服务的 Service 配置
apiVersion: v1
kind: Service
metadata:
  name: paper-py-service
  labels:
    app: paper-py-service
spec:
  selector:
    app: paper-py-service
  ports:
    - protocol: TCP
      port: 9528
      targetPort: 9528
  type: ClusterIP
