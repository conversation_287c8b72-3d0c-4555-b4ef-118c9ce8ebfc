# PowerShell 脚本 - 将本地镜像加载到 Kubernetes 集群
param(
    [string]$Namespace = "paper-services"
)

Write-Host "=== 加载本地镜像到 Kubernetes 集群 ===" -ForegroundColor Green

# 检查 Docker 是否运行
try {
    docker version | Out-Null
    Write-Host "✓ Docker 运行正常" -ForegroundColor Green
} catch {
    Write-Error "Docker 未运行，请启动 Docker Desktop"
    exit 1
}

# 检查 kubectl 是否可用
try {
    kubectl version --client | Out-Null
    Write-Host "✓ kubectl 可用" -ForegroundColor Green
} catch {
    Write-Error "kubectl 未安装或不可用"
    exit 1
}

# 需要加载的镜像列表
$images = @(
    "paper-editor-api:latest",
    "lunwen-generate-ui:latest", 
    "paper-node-service:latest",
    "paper-py-service:latest",
    "paper-api-service:latest"
)

Write-Host "开始加载镜像到 Kubernetes 集群..." -ForegroundColor Yellow

foreach ($image in $images) {
    Write-Host "检查镜像: $image" -ForegroundColor Cyan
    
    # 检查镜像是否存在
    $imageExists = docker images --format "table {{.Repository}}:{{.Tag}}" | Select-String -Pattern "^$image$"
    
    if ($imageExists) {
        Write-Host "  ✓ 镜像 $image 存在" -ForegroundColor Green
        
        # 对于 Docker Desktop 的 Kubernetes，镜像应该已经可用
        # 但我们可以尝试重新标记镜像以确保可用性
        Write-Host "  → 确保镜像在集群中可用..." -ForegroundColor Yellow
        
        # 尝试将镜像保存并重新加载（这对某些 Docker Desktop 配置有帮助）
        $tempFile = "$env:TEMP\$($image.Replace(':', '_').Replace('/', '_')).tar"
        
        try {
            Write-Host "    保存镜像到临时文件..." -ForegroundColor Gray
            docker save -o $tempFile $image

            Write-Host "    重新加载镜像..." -ForegroundColor Gray
            docker load -i $tempFile

            Write-Host "    清理临时文件..." -ForegroundColor Gray
            Remove-Item $tempFile -Force

            Write-Host "  ✓ 镜像 $image 已重新加载" -ForegroundColor Green
        } catch {
            Write-Warning "  ⚠ 镜像 $image 重新加载失败，但可能仍然可用: $($_.Exception.Message)"
        }
    } else {
        Write-Warning "  ⚠ 镜像 $image 不存在，请先构建镜像"
    }
}

Write-Host "`n=== 镜像加载完成 ===" -ForegroundColor Green
Write-Host "提示: 如果 Pod 仍然无法启动，请尝试:" -ForegroundColor Cyan
Write-Host "1. 重新构建镜像: .\build-images.ps1" -ForegroundColor White
Write-Host "2. 重启 Docker Desktop" -ForegroundColor White
Write-Host "3. 检查 Pod 状态: kubectl describe pod <pod-name> -n $Namespace" -ForegroundColor White
