# Node.js 服务的 Deployment 配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paper-node-service-deployment
  labels:
    app: paper-node-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paper-node-service
  template:
    metadata:
      labels:
        app: paper-node-service
    spec:
      containers:
      - name: paper-node-service
        image: paper-node-service:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 9529
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "9529"
        volumeMounts:
        - name: node-temp-volume
          mountPath: /app/temp
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: node-temp-volume
        persistentVolumeClaim:
          claimName: paper-node-temp-pvc
---
# Node.js 服务的 Service 配置
apiVersion: v1
kind: Service
metadata:
  name: paper-node-service
  labels:
    app: paper-node-service
spec:
  selector:
    app: paper-node-service
  ports:
    - protocol: TCP
      port: 9529
      targetPort: 9529
  type: ClusterIP
