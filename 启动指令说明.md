# 论文生成服务启动指令说明

## 概述

本项目提供了一键启动脚本，支持两种运行模式：**开发模式** 和 **生产模式**。

## 服务架构

### 核心服务
1. **Dify** - AI 应用开发平台
2. **Paper Editor API** - 论文编辑器后端服务 (Go)
3. **Paper Node Service** - Node.js 处理服务
4. **Paper Python Service** - Python Flask 服务

### 依赖服务 (开发模式)
- PostgreSQL (数据库)
- Elasticsearch (搜索引擎)
- Kibana (ES 管理界面)
- Redis (缓存)
- MinIO (对象存储)
- n8n (工作流自动化)

## 运行模式

### 开发模式 (dev)
- **Dify**: Docker Compose 启动
- **Paper Editor API**: Docker Compose 启动依赖服务 + Go 程序直接运行
- **Paper Node Service**: npm 直接运行
- **Paper Python Service**: Flask 直接运行

### 生产模式 (prod)
- 所有服务均使用 Docker 容器运行

## 使用方法

### macOS/Linux

```bash
# 给脚本执行权限
chmod +x start-services.sh

# 开发模式启动 (默认)
./start-services.sh

# 或明确指定开发模式
./start-services.sh --mode dev

# 生产模式启动
./start-services.sh --mode prod

# 查看帮助
./start-services.sh --help
```

### Windows

```cmd
# 开发模式启动 (默认)
start-services.bat

# 或明确指定开发模式
start-services.bat --mode dev

# 生产模式启动
start-services.bat --mode prod

# 查看帮助
start-services.bat --help
```

## 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| Dify Web UI | 3000 | Web 界面 |
| Dify API | 5001 | API 接口 |
| Paper Editor API | 8890 | 编辑器 API |
| Paper Node Service | 9529 | Node.js 服务 |
| Paper Python Service | 9528 | Python 服务 |
| PostgreSQL | 5432 | 数据库 |
| Elasticsearch | 9200 | 搜索引擎 |
| Kibana | 5601 | ES 管理界面 |
| Redis | 6379 | 缓存 |
| MinIO | 9000 | 对象存储 |
| MinIO Console | 9001 | MinIO 管理界面 |
| n8n | 5678 | 工作流平台 |
| Weaviate | 8080 | 向量数据库 (生产模式) |

## 具体启动指令

### 开发模式启动顺序

1. **启动 Dify 服务**
   ```bash
   cd dify-main/docker
   docker-compose up -d
   ```

2. **启动 Paper Editor API 依赖服务**
   ```bash
   cd paper-editor-api
   docker-compose up -d
   ```

3. **启动 Paper Editor API Go 服务**
   ```bash
   cd paper-editor-api
   go run editor.go -e dev
   ```

4. **启动 Paper Node Service**
   ```bash
   cd paper-node-service
   npm install
   npm start
   ```

5. **启动 Paper Python Service**
   ```bash
   cd paper-py-service
   pip install -r requirements.txt
   export FLASK_APP=app.py
   export FLASK_ENV=development
   python -m flask run --host=0.0.0.0 --port=9528
   ```

### 生产模式启动指令

1. **启动 Dify 服务**
   ```bash
   cd dify-main/docker
   docker-compose up -d
   ```

2. **启动 Paper Editor API**
   ```bash
   cd paper-editor-api
   docker-compose up -d
   ```

3. **启动 Paper Node Service**
   ```bash
   cd paper-node-service
   docker build -t paper-node-service .
   docker run -d --name paper-node-service -p 9529:9529 paper-node-service
   ```

4. **启动 Paper Python Service**
   ```bash
   cd paper-py-service
   docker build -t paper-py-service .
   docker run -d --name paper-py-service -p 9528:9528 paper-py-service
   ```

## 日志查看

### 开发模式
```bash
# 实时查看日志
tail -f logs/paper-editor-api.log
tail -f logs/paper-node-service.log
tail -f logs/paper-py-service.log

# Docker 服务日志
cd dify-main/docker && docker-compose logs -f
cd paper-editor-api && docker-compose logs -f
```

### 生产模式
```bash
# Docker 容器日志
docker logs -f paper-node-service
docker logs -f paper-py-service
cd dify-main/docker && docker-compose logs -f
cd paper-editor-api && docker-compose logs -f
```

## 停止服务

### 手动停止
```bash
# 停止 Docker Compose 服务
cd dify-main/docker && docker-compose down
cd paper-editor-api && docker-compose down

# 停止单独的 Docker 容器 (生产模式)
docker stop paper-node-service paper-py-service
docker rm paper-node-service paper-py-service

# 停止进程 (开发模式)
pkill -f "go run editor.go"
pkill -f "npm start"
pkill -f "flask run"
```

### 使用脚本停止
按 `Ctrl+C` 停止启动脚本，脚本会自动清理所有进程。

## 系统要求

### 必需软件
- Docker & Docker Compose
- Go 1.20+ (开发模式)
- Node.js 16+ & npm (开发模式)
- Python 3.8+ & pip (开发模式)

### 硬件要求
- 内存: 至少 8GB (推荐 16GB)
- 磁盘: 至少 10GB 可用空间
- CPU: 多核处理器

## 故障排除

### 常见问题

1. **端口被占用**
   - 检查端口: `lsof -i :端口号` (macOS/Linux) 或 `netstat -ano | findstr :端口号` (Windows)
   - 停止占用进程或更改服务端口

2. **Docker 服务启动失败**
   - 检查 Docker 是否运行: `docker ps`
   - 查看错误日志: `docker-compose logs`

3. **Go 编译错误**
   - 更新依赖: `go mod tidy`
   - 检查 Go 版本: `go version`

4. **Node.js 依赖安装失败**
   - 清理缓存: `npm cache clean --force`
   - 删除 node_modules 重新安装

5. **Python 依赖安装失败**
   - 升级 pip: `pip install --upgrade pip`
   - 使用虚拟环境: `python -m venv venv && source venv/bin/activate`

### 调试技巧

1. **查看详细日志**
   - 开发模式: 查看 `logs/` 目录下的日志文件
   - 生产模式: 使用 `docker logs` 命令

2. **检查服务健康状态**
   - 访问各服务的健康检查端点
   - 使用 `curl` 测试 API 连通性

3. **资源监控**
   - 使用 `docker stats` 查看容器资源使用情况
   - 使用 `htop` 或 `top` 查看系统资源

## 开发建议

1. **开发环境使用开发模式**，便于调试和热重载
2. **生产环境使用生产模式**，确保环境一致性
3. **定期清理 Docker 资源**: `docker system prune -f`
4. **使用版本控制管理配置文件**
5. **监控日志文件大小**，定期清理或轮转
