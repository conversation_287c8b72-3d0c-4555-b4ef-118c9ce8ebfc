# 论文生成服务一键启动脚本

本项目提供了多个平台的一键启动脚本，用于快速启动所有相关服务。

## 服务列表

脚本将启动以下服务：

1. **Dify** - AI 应用开发平台
   - Web UI: http://localhost:3000
   - API: http://localhost:5001
   - 启动方式: `cd dify-main/docker && docker-compose up -d`

2. **Paper Editor API** - 论文编辑器后端服务 (Go)
   - API: http://localhost:8890
   - 启动方式: `go run editor.go -f etc/editor-api-dev.yaml`

3. **Paper Node Service** - Node.js 服务
   - API: http://localhost:9529
   - 启动方式: `npm start`

4. **Paper Python Service** - Python Flask 服务
   - API: http://localhost:9528
   - 启动方式: `python -m flask run --host=0.0.0.0 --port=9528`

## 系统要求

### 必需软件

- **Docker** 和 **Docker Compose**
- **Go** (1.20+)
- **Node.js** (16+) 和 **npm**
- **Python** (3.8+) 和 **pip**

### 端口要求

确保以下端口未被占用：
- 3000 (Dify Web UI)
- 5001 (Dify API)
- 8890 (Paper Editor API)
- 9529 (Paper Node Service)
- 9528 (Paper Python Service)
- 8080 (Weaviate)
- 5432 (PostgreSQL)

## 使用方法

### Linux/macOS

#### 启动所有服务
```bash
# 给脚本执行权限
chmod +x start-services.sh stop-services.sh restart-services.sh

# 启动所有服务
./start-services.sh
```

#### 停止所有服务
```bash
./stop-services.sh
```

#### 重启所有服务
```bash
./restart-services.sh
```

### Windows

#### 方法一：使用 PowerShell (推荐)
```powershell
# 启动所有服务
.\start-services.ps1

# 仅停止服务
.\start-services.ps1 -StopOnly

# 显示详细日志
.\start-services.ps1 -Verbose

# 显示帮助
.\start-services.ps1 -Help
```

#### 方法二：使用批处理文件
```cmd
# 启动所有服务
start-services.bat

# 停止所有服务
stop-services.bat
```

## 脚本功能

### 启动脚本功能
- ✅ 检查系统依赖 (Docker, Go, Node.js, Python)
- ✅ 检查端口占用情况
- ✅ 创建必要的数据目录
- ✅ 按顺序启动所有服务
- ✅ 等待服务启动并检查健康状态
- ✅ 显示服务访问地址
- ✅ 提供日志查看方法
- ✅ 优雅的错误处理和清理

### 停止脚本功能
- ✅ 停止所有运行中的服务
- ✅ 清理临时文件
- ✅ 检查端口释放情况
- ✅ 强制停止占用端口的进程

## 日志查看

### 实时日志
```bash
# Linux/macOS
tail -f logs/paper-editor-api.log
tail -f logs/paper-node-service.log
tail -f logs/paper-py-service.log

# Dify 日志
cd dify-main/docker && docker-compose logs -f
```

```powershell
# Windows PowerShell
Get-Content logs\paper-editor-api.log -Wait
Get-Content logs\paper-node-service.log -Wait
Get-Content logs\paper-py-service.log -Wait
```

### 查看所有日志文件
日志文件保存在 `logs/` 目录下：
- `paper-editor-api.log` - Go 服务日志
- `paper-node-service.log` - Node.js 服务日志
- `paper-py-service.log` - Python 服务日志

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :3000  # Linux/macOS
   netstat -ano | findstr :3000  # Windows
   
   # 停止占用端口的进程
   kill -9 <PID>  # Linux/macOS
   taskkill /PID <PID> /F  # Windows
   ```

2. **Docker 服务启动失败**
   ```bash
   # 检查 Docker 状态
   docker ps -a
   docker-compose ps
   
   # 查看 Docker 日志
   cd dify-main/docker
   docker-compose logs
   ```

3. **Go 服务编译失败**
   ```bash
   cd paper-editor-api
   go mod tidy
   go mod download
   ```

4. **Node.js 依赖安装失败**
   ```bash
   cd paper-node-service
   rm -rf node_modules package-lock.json
   npm install
   ```

5. **Python 依赖安装失败**
   ```bash
   cd paper-py-service
   pip install -r requirements.txt --upgrade
   ```

### 手动启动服务

如果自动脚本失败，可以手动启动各个服务：

```bash
# 1. 启动 Dify
cd dify-main/docker
docker-compose up -d

# 2. 启动 Paper Editor API
cd ../../paper-editor-api
go run editor.go -f etc/editor-api-dev.yaml

# 3. 启动 Paper Node Service
cd ../paper-node-service
npm start

# 4. 启动 Paper Python Service
cd ../paper-py-service
export FLASK_APP=app.py
export FLASK_ENV=development
python -m flask run --host=0.0.0.0 --port=9528
```

## 开发模式

如果需要在开发模式下运行服务，可以：

1. 修改配置文件中的环境变量
2. 使用开发专用的配置文件
3. 启用热重载功能

```bash
# Node.js 开发模式
cd paper-node-service
npm run dev  # 使用 nodemon

# Python 开发模式
cd paper-py-service
export FLASK_ENV=development
python -m flask run --debug
```

## 贡献

如果您发现脚本有问题或需要改进，请：

1. 提交 Issue 描述问题
2. 提供错误日志
3. 说明您的操作系统和软件版本
4. 提交 Pull Request 修复问题

## 许可证

本脚本遵循项目的开源许可证。
