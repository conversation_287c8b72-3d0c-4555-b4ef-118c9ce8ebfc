# 论文生成服务一键启动脚本 (PowerShell版)
# 启动 dify-main、paper-editor-api、paper-node-service、paper-py-service

param(
    [switch]$Help,
    [switch]$StopOnly,
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green"
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
        "Cyan" = "Cyan"
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Log-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Log-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Log-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Log-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

# 显示帮助信息
if ($Help) {
    Write-Host @"
论文生成服务一键启动脚本

用法:
    .\start-services.ps1              # 启动所有服务
    .\start-services.ps1 -StopOnly    # 仅停止所有服务
    .\start-services.ps1 -Verbose     # 显示详细日志
    .\start-services.ps1 -Help        # 显示此帮助信息

服务包括:
    - Dify (Docker Compose)
    - Paper Editor API (Go)
    - Paper Node Service (Node.js)
    - Paper Python Service (Python Flask)
"@
    exit 0
}

# 检查命令是否存在
function Test-Command {
    param([string]$Command)
    
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 检查端口是否被占用
function Test-Port {
    param([int]$Port)
    
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $Port -InformationLevel Quiet -WarningAction SilentlyContinue
        return $connection
    }
    catch {
        return $false
    }
}

# 等待服务启动
function Wait-ForService {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$MaxAttempts = 30
    )
    
    Log-Info "等待 $ServiceName 启动..."
    
    for ($i = 1; $i -le $MaxAttempts; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Log-Success "$ServiceName 启动成功"
                return $true
            }
        }
        catch {
            # 忽略错误，继续等待
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
    }
    
    Write-Host ""
    Log-Error "$ServiceName 启动超时"
    return $false
}

# 停止服务函数
function Stop-Services {
    Log-Info "正在停止所有服务..."
    
    # 停止 Docker Compose 服务
    try {
        Push-Location "dify-main\docker"
        docker-compose down
        Pop-Location
        Log-Success "Dify 服务已停止"
    }
    catch {
        Log-Warning "停止 Dify 服务时出现问题: $($_.Exception.Message)"
    }
    
    # 停止其他进程
    $processNames = @("editor", "node", "python", "flask")
    foreach ($processName in $processNames) {
        try {
            Get-Process -Name $processName -ErrorAction SilentlyContinue | Stop-Process -Force
        }
        catch {
            # 忽略错误
        }
    }
    
    Log-Success "所有服务已停止"
}

# 如果只是停止服务
if ($StopOnly) {
    Stop-Services
    exit 0
}

# 清理函数
function Cleanup {
    Log-Info "正在清理..."
    Stop-Services
}

# 设置清理处理
Register-EngineEvent PowerShell.Exiting -Action { Cleanup }

try {
    Write-ColorOutput "🚀 开始启动论文生成服务..." "Cyan"
    
    # 检查系统依赖
    Log-Info "检查系统依赖..."
    
    $requiredCommands = @("docker", "docker-compose", "go", "node", "python")
    foreach ($cmd in $requiredCommands) {
        if (-not (Test-Command $cmd)) {
            Log-Error "$cmd 未安装，请先安装 $cmd"
            exit 1
        }
    }
    
    # 检查端口占用
    Log-Info "检查端口占用情况..."
    $ports = @(3000, 5001, 8890, 9529, 9528, 8080, 5432)
    foreach ($port in $ports) {
        if (Test-Port $port) {
            Log-Error "端口 $port 已被占用，请先停止占用该端口的服务"
            exit 1
        }
    }
    
    # 创建必要的目录
    Log-Info "创建数据目录..."
    $directories = @(
        "volumes\dify\db",
        "volumes\dify\redis", 
        "volumes\dify\storage",
        "volumes\paper-editor\postgres",
        "volumes\weaviate",
        "paper-editor-api\uploads",
        "logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    # 启动 Dify 服务
    Log-Info "启动 Dify 服务..."
    Push-Location "dify-main\docker"
    Start-Process -FilePath "docker-compose" -ArgumentList "up", "-d" -Wait -NoNewWindow
    Pop-Location
    
    # 等待 Dify 服务启动
    Start-Sleep -Seconds 10
    
    # 启动 Paper Editor API 服务
    Log-Info "启动 Paper Editor API 服务..."
    Push-Location "paper-editor-api"
    Start-Process -FilePath "go" -ArgumentList "mod", "tidy" -Wait -NoNewWindow
    $editorProcess = Start-Process -FilePath "go" -ArgumentList "run", "editor.go", "-f", "etc\editor-api-dev.yaml" -PassThru -RedirectStandardOutput "..\logs\paper-editor-api.log" -RedirectStandardError "..\logs\paper-editor-api-error.log"
    Pop-Location
    
    # 启动 Paper Node Service
    Log-Info "启动 Paper Node Service..."
    Push-Location "paper-node-service"
    Start-Process -FilePath "npm" -ArgumentList "install" -Wait -NoNewWindow
    $nodeProcess = Start-Process -FilePath "npm" -ArgumentList "start" -PassThru -RedirectStandardOutput "..\logs\paper-node-service.log" -RedirectStandardError "..\logs\paper-node-service-error.log"
    Pop-Location
    
    # 启动 Paper Python Service
    Log-Info "启动 Paper Python Service..."
    Push-Location "paper-py-service"
    Start-Process -FilePath "pip" -ArgumentList "install", "-r", "requirements.txt" -Wait -NoNewWindow
    $env:FLASK_APP = "app.py"
    $env:FLASK_ENV = "development"
    $pythonProcess = Start-Process -FilePath "python" -ArgumentList "-m", "flask", "run", "--host=0.0.0.0", "--port=9528" -PassThru -RedirectStandardOutput "..\logs\paper-py-service.log" -RedirectStandardError "..\logs\paper-py-service-error.log"
    Pop-Location
    
    # 等待所有服务启动
    Log-Info "等待所有服务启动完成..."
    Start-Sleep -Seconds 30
    
    # 检查服务状态
    Log-Info "检查服务状态..."
    
    $services = @(
        @{ Url = "http://localhost:3000"; Name = "Dify Web UI" },
        @{ Url = "http://localhost:5001/health"; Name = "Dify API" },
        @{ Url = "http://localhost:8890/health"; Name = "Paper Editor API" },
        @{ Url = "http://localhost:9529/health"; Name = "Paper Node Service" },
        @{ Url = "http://localhost:9528/health"; Name = "Paper Python Service" }
    )
    
    foreach ($service in $services) {
        if (Wait-ForService -Url $service.Url -ServiceName $service.Name) {
            Log-Success "$($service.Name) 运行正常"
        }
        else {
            Log-Warning "$($service.Name) 可能需要更多时间启动"
        }
    }
    
    Write-Host ""
    Log-Success "所有服务启动完成！"
    Write-Host ""
    Write-ColorOutput "🌐 服务访问地址：" "Cyan"
    Write-Host "  - Dify Web UI:        http://localhost:3000"
    Write-Host "  - Dify API:           http://localhost:5001"
    Write-Host "  - Paper Editor API:   http://localhost:8890"
    Write-Host "  - Paper Node Service: http://localhost:9529"
    Write-Host "  - Paper Python Service: http://localhost:9528"
    Write-Host "  - Weaviate:           http://localhost:8080"
    Write-Host "  - PostgreSQL:         localhost:5432"
    Write-Host ""
    Write-ColorOutput "📝 查看日志：" "Cyan"
    Write-Host "  - Dify: cd dify-main\docker && docker-compose logs -f"
    Write-Host "  - Paper Editor API: Get-Content logs\paper-editor-api.log -Wait"
    Write-Host "  - Paper Node Service: Get-Content logs\paper-node-service.log -Wait"
    Write-Host "  - Paper Python Service: Get-Content logs\paper-py-service.log -Wait"
    Write-Host ""
    Write-ColorOutput "🛑 停止所有服务：" "Cyan"
    Write-Host "  .\start-services.ps1 -StopOnly"
    Write-Host ""
    Write-ColorOutput "按 Ctrl+C 停止所有服务..." "Yellow"
    
    # 保持脚本运行
    while ($true) {
        Start-Sleep -Seconds 1
    }
}
catch {
    Log-Error "启动过程中出现错误: $($_.Exception.Message)"
    Cleanup
    exit 1
}
