# 使用 Go 官方镜像作为构建环境
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache git

# 复制 go.mod 和 go.sum
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o editor-api .

# 使用轻量级镜像作为运行环境
FROM alpine:latest

# 安装 ca-certificates 用于 HTTPS 请求
RUN apk --no-cache add ca-certificates

WORKDIR /root/

# 从构建阶段复制二进制文件
COPY --from=builder /app/editor-api .

# 复制配置文件
COPY --from=builder /app/etc ./etc

# 创建上传目录
RUN mkdir -p uploads

# 暴露端口
EXPOSE 8890

# 启动命令
CMD ["./editor-api", "-e", "prod"]
